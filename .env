# 数据库配置
POSTGRES_DB=ruoyi-vue-pro
POSTGRES_USER=postgres
POSTGRES_PASSWORD=123456
POSTGRES_PORT=5432

# Redis 配置
REDIS_PASSWORD=
REDIS_PORT=6379

# Nacos 配置
NACOS_PORT=8848
NACOS_GRPC_PORT=9848
NACOS_AUTH_TOKEN=SecretKey012345678901234567890123456789012345678901234567890123456789
NACOS_AUTH_IDENTITY_KEY=nacos
NACOS_AUTH_IDENTITY_VALUE=nacos

# 应用端口配置
GATEWAY_PORT=48080
SYSTEM_PORT=48081
YUDAO_PORT=9000

# JVM 配置
GATEWAY_JAVA_OPTS=-Xms512m -Xmx1024m -Djava.security.egd=file:/dev/./urandom
SYSTEM_JAVA_OPTS=-Xms512m -Xmx1024m -Djava.security.egd=file:/dev/./urandom
YUDAO_JAVA_OPTS=-Xms1024m -Xmx2048m -Djava.security.egd=file:/dev/./urandom
