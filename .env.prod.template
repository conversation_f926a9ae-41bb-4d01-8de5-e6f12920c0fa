# 生产环境配置模板
# 复制此文件为 .env.prod 并修改相应的值

# 数据库配置
POSTGRES_DB=ruoyi-vue-pro
POSTGRES_USER=postgres
POSTGRES_PASSWORD=YOUR_SECURE_DATABASE_PASSWORD_HERE
POSTGRES_PORT=5432

# Redis 配置
REDIS_PASSWORD=YOUR_SECURE_REDIS_PASSWORD_HERE
REDIS_PORT=6379

# Nacos 配置
NACOS_PORT=8848
NACOS_GRPC_PORT=9848
NACOS_AUTH_TOKEN=YOUR_SECURE_NACOS_TOKEN_HERE_MUST_BE_32_CHARS_OR_MORE
NACOS_AUTH_IDENTITY_KEY=nacos
NACOS_AUTH_IDENTITY_VALUE=YOUR_SECURE_NACOS_PASSWORD_HERE

# 应用端口配置
GATEWAY_PORT=48080
SYSTEM_PORT=48081
YUDAO_PORT=9000

# JVM 配置 - 生产环境
GATEWAY_JAVA_OPTS=-Xms1g -Xmx2g -Djava.security.egd=file:/dev/./urandom
SYSTEM_JAVA_OPTS=-Xms1g -Xmx2g -Djava.security.egd=file:/dev/./urandom
YUDAO_JAVA_OPTS=-Xms2g -Xmx4g -Djava.security.egd=file:/dev/./urandom

# 安全提示:
# 1. 请修改所有默认密码
# 2. NACOS_AUTH_TOKEN 必须至少32个字符
# 3. 建议使用强密码生成器生成密码
# 4. 不要将此文件提交到版本控制系统
