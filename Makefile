# 芋道云 Docker 部署 Makefile
# 使用方法: make [target]

.PHONY: help build up down restart logs status clean build-jars

# 默认目标
help:
	@echo "芋道云 Docker 部署命令:"
	@echo ""
	@echo "开发环境:"
	@echo "  make build-jars    - 构建 JAR 文件"
	@echo "  make build         - 构建 Docker 镜像"
	@echo "  make up            - 启动所有服务"
	@echo "  make down          - 停止所有服务"
	@echo "  make restart       - 重启所有服务"
	@echo "  make logs          - 查看所有服务日志"
	@echo "  make status        - 查看服务状态"
	@echo ""
	@echo "生产环境:"
	@echo "  make prod-up       - 启动生产环境"
	@echo "  make prod-down     - 停止生产环境"
	@echo "  make prod-logs     - 查看生产环境日志"
	@echo ""
	@echo "维护:"
	@echo "  make clean         - 清理未使用的资源"
	@echo "  make backup-db     - 备份数据库"
	@echo "  make restore-db    - 恢复数据库"

# 构建 JAR 文件
build-jars:
	@echo "构建 JAR 文件..."
	mvn clean package -DskipTests

# 构建 Docker 镜像
build:
	@echo "构建 Docker 镜像..."
	docker-compose build

# 启动开发环境
up:
	@echo "启动开发环境..."
	docker-compose up -d

# 停止开发环境
down:
	@echo "停止开发环境..."
	docker-compose down

# 重启开发环境
restart:
	@echo "重启开发环境..."
	docker-compose restart

# 查看日志
logs:
	@echo "查看服务日志..."
	docker-compose logs -f

# 查看服务状态
status:
	@echo "查看服务状态..."
	docker-compose ps

# 启动生产环境
prod-up:
	@echo "启动生产环境..."
	docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d

# 停止生产环境
prod-down:
	@echo "停止生产环境..."
	docker-compose -f docker-compose.prod.yml --env-file .env.prod down

# 查看生产环境日志
prod-logs:
	@echo "查看生产环境日志..."
	docker-compose -f docker-compose.prod.yml --env-file .env.prod logs -f

# 清理未使用的资源
clean:
	@echo "清理未使用的 Docker 资源..."
	docker system prune -f
	docker volume prune -f

# 备份数据库
backup-db:
	@echo "备份数据库..."
	mkdir -p backup
	docker-compose exec postgres pg_dump -U postgres ruoyi-vue-pro > backup/backup-$(shell date +%Y%m%d-%H%M%S).sql
	@echo "备份完成: backup/backup-$(shell date +%Y%m%d-%H%M%S).sql"

# 恢复数据库 (需要指定备份文件)
restore-db:
	@echo "请指定备份文件: make restore-db FILE=backup/backup-20231201-120000.sql"
	@if [ -z "$(FILE)" ]; then \
		echo "错误: 请指定备份文件"; \
		exit 1; \
	fi
	@echo "恢复数据库从: $(FILE)"
	docker-compose exec -T postgres psql -U postgres ruoyi-vue-pro < $(FILE)

# 快速启动 (构建 + 启动)
quick-start: build-jars build up
	@echo "快速启动完成!"

# 完全重置 (停止 + 清理 + 重新启动)
reset: down clean build-jars build up
	@echo "完全重置完成!"
