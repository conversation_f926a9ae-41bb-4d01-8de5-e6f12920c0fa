# 芋道云 Docker 部署指南

本文档提供了使用 Docker Compose 部署芋道云项目的完整指南。

## 📋 目录

- [系统要求](#系统要求)
- [快速开始](#快速开始)
- [服务架构](#服务架构)
- [配置说明](#配置说明)
- [常用命令](#常用命令)
- [故障排除](#故障排除)
- [生产环境部署](#生产环境部署)

## 🔧 系统要求

### 必需软件
- **Docker**: 20.10.0 或更高版本
- **Docker Compose**: 2.0.0 或更高版本
- **Maven**: 3.6.0 或更高版本 (用于构建 JAR 文件)
- **JDK**: 21 或更高版本 (用于构建)

### 硬件要求
- **内存**: 最少 4GB，推荐 8GB 或更多
- **磁盘**: 最少 10GB 可用空间
- **CPU**: 2核心或更多

## 🚀 快速开始

### 1. 构建项目

首先需要构建项目生成 JAR 文件：

```bash
# 清理并构建项目
mvn clean package -DskipTests

# 或者只构建特定模块
mvn clean package -DskipTests -pl yudao-gateway,yudao-module-system/yudao-module-system-server,yudao-server
```

### 2. 启动服务

#### Windows 用户
```cmd
# 运行启动脚本
docker-start.bat
```

#### Linux/macOS 用户
```bash
# 给脚本执行权限
chmod +x docker-start.sh

# 运行启动脚本
./docker-start.sh
```

#### 手动启动
```bash
# 启动所有服务
docker-compose up -d

# 或者分步启动
docker-compose up -d postgres redis nacos
# 等待基础服务启动完成后
docker-compose up -d gateway-server system-server yudao-server
```

### 3. 验证部署

访问以下地址验证服务是否正常运行：

- **网关服务**: http://localhost:48080
- **系统服务**: http://localhost:48081  
- **主服务**: http://localhost:9000
- **Nacos控制台**: http://localhost:8848/nacos

## 🏗️ 服务架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Gateway       │    │   System        │    │   Yudao         │
│   (48080)       │    │   (48081)       │    │   (9000)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │     Redis       │    │     Nacos       │
│   (5432)        │    │     (6379)      │    │     (8848)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 服务说明

| 服务名 | 端口 | 说明 | 健康检查 |
|--------|------|------|----------|
| gateway-server | 48080 | Spring Cloud Gateway 网关服务 | /actuator/health |
| system-server | 48081 | 系统管理服务 | /actuator/health |
| yudao-server | 9000 | 主业务服务 | /actuator/health |
| postgres | 5432 | PostgreSQL 数据库 | pg_isready |
| redis | 6379 | Redis 缓存 | redis-cli ping |
| nacos | 8848 | 注册中心和配置中心 | /nacos/v1/console/health/readiness |

## ⚙️ 配置说明

### 环境变量配置 (.env)

```bash
# 数据库配置
POSTGRES_DB=ruoyi-vue-pro
POSTGRES_USER=postgres
POSTGRES_PASSWORD=123456
POSTGRES_PORT=5432

# Redis 配置
REDIS_PASSWORD=
REDIS_PORT=6379

# Nacos 配置
NACOS_PORT=8848
NACOS_GRPC_PORT=9848

# 应用端口配置
GATEWAY_PORT=48080
SYSTEM_PORT=48081
YUDAO_PORT=9000

# JVM 配置
GATEWAY_JAVA_OPTS=-Xms512m -Xmx1024m
SYSTEM_JAVA_OPTS=-Xms512m -Xmx1024m
YUDAO_JAVA_OPTS=-Xms1024m -Xmx2048m
```

### 配置文件说明

- `docker-compose.yml`: 主要的 Docker Compose 配置文件
- `docker-compose.override.yml`: 本地开发环境覆盖配置
- `.env`: 环境变量配置文件
- `application-docker.yaml`: 各服务的 Docker 环境配置

## 📝 常用命令

### 服务管理
```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启特定服务
docker-compose restart gateway-server

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f gateway-server

# 进入容器
docker-compose exec postgres bash
```

### 数据管理
```bash
# 备份数据库
docker-compose exec postgres pg_dump -U postgres ruoyi-vue-pro > backup.sql

# 恢复数据库
docker-compose exec -T postgres psql -U postgres ruoyi-vue-pro < backup.sql

# 清理数据卷
docker-compose down -v
```

### 镜像管理
```bash
# 重新构建镜像
docker-compose build

# 拉取最新镜像
docker-compose pull

# 清理未使用的镜像
docker image prune -f
```

## 🔍 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 查看详细日志
docker-compose logs [服务名]

# 检查容器状态
docker-compose ps

# 重启服务
docker-compose restart [服务名]
```

#### 2. 数据库连接失败
- 确保 PostgreSQL 服务已启动
- 检查数据库配置是否正确
- 验证网络连接

#### 3. 内存不足
- 调整 JVM 参数
- 增加系统内存
- 优化服务配置

#### 4. 端口冲突
- 修改 `.env` 文件中的端口配置
- 检查端口是否被其他程序占用

### 健康检查

```bash
# 检查所有服务健康状态
docker-compose ps

# 检查特定服务
curl http://localhost:48080/actuator/health
curl http://localhost:48081/actuator/health
curl http://localhost:9000/actuator/health
```

## 🚀 生产环境部署

### 安全配置

1. **修改默认密码**
```bash
# 修改 .env 文件中的密码
POSTGRES_PASSWORD=your_secure_password
REDIS_PASSWORD=your_redis_password
```

2. **启用 HTTPS**
- 配置 SSL 证书
- 修改网关配置

3. **网络安全**
- 使用防火墙限制端口访问
- 配置 VPN 或内网访问

### 性能优化

1. **JVM 调优**
```bash
# 根据服务器配置调整内存
YUDAO_JAVA_OPTS=-Xms2g -Xmx4g -XX:+UseG1GC
```

2. **数据库优化**
- 配置连接池
- 优化查询性能
- 定期备份数据

3. **监控配置**
- 配置日志收集
- 设置性能监控
- 配置告警机制

### 高可用部署

1. **负载均衡**
- 使用 Nginx 或 HAProxy
- 配置多实例部署

2. **数据库集群**
- 配置主从复制
- 使用数据库集群

3. **缓存集群**
- Redis 集群配置
- 缓存高可用

## 📞 支持

如果遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查项目的 GitHub Issues
3. 查看官方文档: https://doc.iocoder.cn/
4. 联系技术支持

---

**注意**: 本部署方案适用于开发和测试环境。生产环境部署请参考生产环境部署章节进行相应的安全和性能配置。
