# 简化的 Docker 配置文件，用于调试

server:
  port: 48081

spring:
  application:
    name: yudao-module-system-server
  
  # 数据源配置
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: *********************************************
          username: postgres
          password: 123456
          driver-class-name: org.postgresql.Driver
  
  # Redis 配置
  data:
    redis:
      host: redis
      port: 6379
      database: 0

  # 暂时禁用 Nacos
  cloud:
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false

# 日志配置
logging:
  level:
    root: INFO
    cn.iocoder.yudao: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 监控端点
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always
