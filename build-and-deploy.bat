@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 芋道云完整构建和部署脚本 (Windows版本)
:: 作者: Augment Agent

echo.
echo ==================================================
echo           芋道云完整构建和部署脚本
echo ==================================================
echo.

:: 清除代理设置
echo [步骤1] 清除代理设置...
set http_proxy=
set https_proxy=
set HTTP_PROXY=
set HTTPS_PROXY=
set no_proxy=
set NO_PROXY=
set all_proxy=
set ALL_PROXY=
echo [信息] 代理已清除

:: 检查当前目录
echo [步骤1.5] 检查当前目录...
echo 当前目录: %CD%
if not exist "docker-compose.yml" (
    echo [错误] 未找到 docker-compose.yml 文件
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

:: 检查 Java 环境
echo.
echo [步骤2] 检查 Java 环境...
java -version >nul 2>&1
if errorlevel 1 (
    echo [错误] Java 未安装，请先安装 JDK 21 或更高版本
    pause
    exit /b 1
)
echo [信息] Java 环境检查通过

:: 检查 Maven
echo.
echo [步骤3] 检查 Maven 环境...
mvn -version >nul 2>&1
if errorlevel 1 (
    if exist "mvnw.cmd" (
        echo [信息] 使用项目 Maven Wrapper
        set MVN_CMD=mvnw.cmd
    ) else (
        echo [错误] Maven 未找到，请安装 Maven 或确保 mvnw.cmd 文件存在
        pause
        exit /b 1
    )
) else (
    echo [信息] 使用系统 Maven
    set MVN_CMD=mvn
)

:: 清理和构建项目
echo.
echo [步骤4] 清理和构建项目...
echo [信息] 执行: %MVN_CMD% clean package -DskipTests
%MVN_CMD% clean package -DskipTests
if errorlevel 1 (
    echo [错误] Maven 构建失败
    pause
    exit /b 1
)

:: 检查构建结果
echo.
echo [步骤5] 检查构建结果...

if not exist "yudao-gateway\target\yudao-gateway.jar" (
    echo [错误] yudao-gateway.jar 构建失败
    pause
    exit /b 1
) else (
    echo [信息] ✓ yudao-gateway.jar 构建成功
)

if not exist "yudao-module-system\yudao-module-system-server\target\yudao-module-system-server.jar" (
    echo [错误] yudao-module-system-server.jar 构建失败
    pause
    exit /b 1
) else (
    echo [信息] ✓ yudao-module-system-server.jar 构建成功
)

if not exist "yudao-server\target\yudao-server.jar" (
    echo [错误] yudao-server.jar 构建失败
    pause
    exit /b 1
) else (
    echo [信息] ✓ yudao-server.jar 构建成功
)

:: 检查 Docker
echo.
echo [步骤6] 检查 Docker 环境...
docker --version >nul 2>&1
if errorlevel 1 (
    echo [错误] Docker 未安装，请先安装 Docker Desktop
    pause
    exit /b 1
)

docker info >nul 2>&1
if errorlevel 1 (
    echo [错误] Docker 服务未运行，请启动 Docker Desktop
    pause
    exit /b 1
)
echo [信息] Docker 环境检查通过

:: 停止现有服务
echo.
echo [步骤7] 停止现有服务...
docker-compose down >nul 2>&1

:: 构建 Docker 镜像
echo.
echo [步骤8] 构建 Docker 镜像...
docker-compose build
if errorlevel 1 (
    echo [错误] Docker 镜像构建失败
    pause
    exit /b 1
)

:: 启动基础服务
echo.
echo [步骤9] 启动基础服务...
docker-compose up -d postgres redis nacos
if errorlevel 1 (
    echo [错误] 基础服务启动失败
    pause
    exit /b 1
)

echo [信息] 等待基础服务启动...
timeout /t 30 /nobreak >nul

:: 启动应用服务
echo.
echo [步骤10] 启动应用服务...
docker-compose up -d
if errorlevel 1 (
    echo [错误] 应用服务启动失败
    pause
    exit /b 1
)

echo [信息] 等待应用服务启动...
timeout /t 60 /nobreak >nul

:: 显示服务状态
echo.
echo [步骤11] 显示服务状态...
docker-compose ps

:: 显示访问信息
echo.
echo === 芋道云服务访问地址 ===
echo 网关服务:        http://localhost:48080
echo 系统服务:        http://localhost:48081
echo 主服务:          http://localhost:9000
echo Nacos控制台:     http://localhost:8848/nacos
echo PostgreSQL:      localhost:5432
echo Redis:           localhost:6379
echo.
echo === 默认账号信息 ===
echo Nacos:           用户名: nacos, 密码: nacos
echo PostgreSQL:      用户名: postgres, 密码: 123456
echo.
echo [信息] 构建和部署完成！
echo.
pause
