#!/bin/bash

# 芋道云完整构建和部署脚本
# 作者: Augment Agent

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 清除代理环境变量
unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY ftp_proxy FTP_PROXY no_proxy NO_PROXY

echo -e "${GREEN}"
echo "=================================================="
echo "           芋道云完整构建和部署脚本"
echo "=================================================="
echo -e "${NC}"

# 检查 Java 环境
print_step "检查 Java 环境..."
if ! command -v java &> /dev/null; then
    print_error "Java 未安装，请先安装 JDK 21 或更高版本"
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
if [ "$JAVA_VERSION" -lt 21 ]; then
    print_warning "建议使用 JDK 21 或更高版本，当前版本: $JAVA_VERSION"
fi

print_message "Java 环境检查通过"

# 检查 Maven
print_step "检查 Maven 环境..."
if command -v mvn &> /dev/null; then
    print_message "使用系统 Maven"
    MVN_CMD="mvn"
elif [ -f "./mvnw" ]; then
    print_message "使用项目 Maven Wrapper"
    chmod +x ./mvnw
    MVN_CMD="./mvnw"
else
    print_error "Maven 未找到，请安装 Maven 或确保 mvnw 文件存在"
    exit 1
fi

# 清理和构建项目
print_step "清理和构建项目..."
print_message "执行: $MVN_CMD clean package -DskipTests"
$MVN_CMD clean package -DskipTests

# 检查构建结果
print_step "检查构建结果..."
JAR_FILES=(
    "yudao-gateway/target/yudao-gateway.jar"
    "yudao-module-system/yudao-module-system-server/target/yudao-module-system-server.jar"
    "yudao-server/target/yudao-server.jar"
)

for jar_file in "${JAR_FILES[@]}"; do
    if [ -f "$jar_file" ]; then
        print_message "✓ $jar_file 构建成功"
    else
        print_error "✗ $jar_file 构建失败"
        exit 1
    fi
done

# 检查 Docker
print_step "检查 Docker 环境..."
if ! command -v docker &> /dev/null; then
    print_error "Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! docker info &> /dev/null; then
    print_error "Docker 服务未运行，请启动 Docker"
    exit 1
fi

print_message "Docker 环境检查通过"

# 停止现有服务
print_step "停止现有服务..."
docker-compose down 2>/dev/null || true

# 构建 Docker 镜像
print_step "构建 Docker 镜像..."
docker-compose build

# 启动基础服务
print_step "启动基础服务..."
docker-compose up -d postgres redis nacos

print_message "等待基础服务启动..."
sleep 30

# 检查基础服务健康状态
print_step "检查基础服务状态..."
for i in {1..10}; do
    if docker-compose ps | grep -q "Up.*healthy"; then
        print_message "基础服务启动成功"
        break
    fi
    if [ $i -eq 10 ]; then
        print_warning "基础服务启动可能有问题，继续启动应用服务..."
    fi
    sleep 5
done

# 启动应用服务
print_step "启动应用服务..."
docker-compose up -d

print_message "等待应用服务启动..."
sleep 60

# 显示服务状态
print_step "显示服务状态..."
docker-compose ps

# 显示访问信息
print_step "服务访问信息:"
echo ""
echo -e "${GREEN}=== 芋道云服务访问地址 ===${NC}"
echo -e "${BLUE}网关服务:${NC}        http://localhost:48080"
echo -e "${BLUE}系统服务:${NC}        http://localhost:48081"
echo -e "${BLUE}主服务:${NC}          http://localhost:9000"
echo -e "${BLUE}Nacos控制台:${NC}     http://localhost:8848/nacos"
echo -e "${BLUE}PostgreSQL:${NC}      localhost:5432"
echo -e "${BLUE}Redis:${NC}           localhost:6379"
echo ""
echo -e "${GREEN}=== 默认账号信息 ===${NC}"
echo -e "${BLUE}Nacos:${NC}           用户名: nacos, 密码: nacos"
echo -e "${BLUE}PostgreSQL:${NC}      用户名: postgres, 密码: 123456"
echo ""

print_message "构建和部署完成！"
