#!/bin/bash

echo "=== System Server 调试启动脚本 ==="
echo

# 1. 停止现有服务
echo "1. 停止现有的 system-server..."
docker-compose stop system-server
docker-compose rm -f system-server

# 2. 确保依赖服务正常
echo "2. 检查依赖服务状态..."
echo "PostgreSQL 状态:"
docker-compose ps postgres
echo "Redis 状态:"
docker-compose ps redis
echo "Nacos 状态:"
docker-compose ps nacos

# 3. 测试依赖服务连接
echo
echo "3. 测试依赖服务连接..."

# 测试 PostgreSQL
echo "测试 PostgreSQL 连接..."
docker-compose exec postgres pg_isready -U postgres -d ruoyi-vue-pro || echo "PostgreSQL 连接失败"

# 测试 Redis
echo "测试 Redis 连接..."
docker-compose exec redis redis-cli ping || echo "Redis 连接失败"

# 测试 Nacos
echo "测试 Nacos 连接..."
curl -s http://localhost:8848/nacos/v1/console/health/readiness || echo "Nacos 连接失败"

# 4. 重新构建镜像
echo
echo "4. 重新构建 system-server 镜像..."
docker-compose build --no-cache system-server

# 5. 使用调试模式启动
echo
echo "5. 启动 system-server（调试模式）..."

# 设置调试环境变量
export SYSTEM_JAVA_OPTS="-Xms256m -Xmx512m -Ddebug=true -Dlogging.level.root=DEBUG -Dspring.profiles.active=docker"

# 启动容器
docker-compose up -d system-server

# 6. 监控启动过程
echo
echo "6. 监控启动过程（30秒）..."
for i in {1..30}; do
    echo "第 $i 秒:"
    
    # 检查容器状态
    STATUS=$(docker inspect yudao-system-server --format='{{.State.Status}}' 2>/dev/null || echo "not_found")
    echo "  容器状态: $STATUS"
    
    if [ "$STATUS" = "running" ]; then
        echo "  ✓ 容器正在运行"
        # 检查日志
        echo "  最新日志:"
        docker logs --tail=5 yudao-system-server 2>&1 | sed 's/^/    /'
    elif [ "$STATUS" = "exited" ]; then
        echo "  ✗ 容器已退出"
        echo "  退出代码: $(docker inspect yudao-system-server --format='{{.State.ExitCode}}' 2>/dev/null)"
        echo "  完整日志:"
        docker logs yudao-system-server 2>&1 | sed 's/^/    /'
        break
    elif [ "$STATUS" = "not_found" ]; then
        echo "  ✗ 容器不存在"
        break
    fi
    
    sleep 1
done

# 7. 最终状态检查
echo
echo "7. 最终状态检查..."
docker-compose ps system-server

echo
echo "如果容器启动失败，请检查上面的日志输出"
echo "常见问题:"
echo "1. 依赖服务未就绪"
echo "2. 配置文件错误"
echo "3. 内存不足"
echo "4. 端口冲突"
