#!/bin/bash

echo "=== System Server 诊断脚本 ==="
echo

# 1. 检查 JAR 文件
echo "1. 检查 JAR 文件..."
JAR_PATH="yudao-module-system/yudao-module-system-server/target/yudao-module-system-server.jar"
if [ -f "$JAR_PATH" ]; then
    echo "✓ JAR 文件存在: $JAR_PATH"
    echo "  文件大小: $(du -h "$JAR_PATH" | cut -f1)"
    echo "  修改时间: $(stat -c %y "$JAR_PATH" 2>/dev/null || stat -f %Sm "$JAR_PATH" 2>/dev/null)"
else
    echo "✗ JAR 文件不存在: $JAR_PATH"
    echo "  需要重新构建项目"
fi
echo

# 2. 检查容器状态
echo "2. 检查容器状态..."
docker ps -a --filter "name=yudao-system-server" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
echo

# 3. 检查容器日志
echo "3. 检查容器日志..."
if docker ps -a --filter "name=yudao-system-server" --format "{{.Names}}" | grep -q "yudao-system-server"; then
    echo "最近 50 行日志:"
    docker logs --tail=50 yudao-system-server 2>&1
else
    echo "容器不存在，尝试查看 docker-compose 日志:"
    docker-compose logs --tail=50 system-server 2>&1
fi
echo

# 4. 检查网络连接
echo "4. 检查依赖服务状态..."
echo "PostgreSQL 状态:"
docker-compose ps postgres
echo
echo "Redis 状态:"
docker-compose ps redis
echo
echo "Nacos 状态:"
docker-compose ps nacos
echo

# 5. 检查端口占用
echo "5. 检查端口占用..."
if command -v netstat >/dev/null 2>&1; then
    echo "端口 48081 占用情况:"
    netstat -tulpn 2>/dev/null | grep :48081 || echo "端口 48081 未被占用"
elif command -v ss >/dev/null 2>&1; then
    echo "端口 48081 占用情况:"
    ss -tulpn 2>/dev/null | grep :48081 || echo "端口 48081 未被占用"
fi
echo

# 6. 尝试手动启动容器
echo "6. 尝试手动启动容器进行调试..."
echo "停止现有容器..."
docker-compose stop system-server 2>/dev/null
docker-compose rm -f system-server 2>/dev/null

echo "尝试构建镜像..."
docker-compose build system-server

echo "尝试启动容器..."
docker-compose up -d system-server

echo "等待 10 秒..."
sleep 10

echo "检查启动后的状态:"
docker-compose ps system-server

echo "检查最新日志:"
docker-compose logs --tail=20 system-server

echo
echo "=== 诊断完成 ==="
echo
echo "如果容器仍然无法启动，请检查:"
echo "1. JAR 文件是否正确构建"
echo "2. 依赖服务是否正常运行"
echo "3. 配置文件是否正确"
echo "4. 内存是否足够"
