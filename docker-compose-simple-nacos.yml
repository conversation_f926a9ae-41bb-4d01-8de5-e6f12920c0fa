services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: yudao-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ruoyi-vue-pro
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123456
      TZ: Asia/Shanghai
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - yudao-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d ruoyi-vue-pro"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: yudao-redis
    restart: unless-stopped
    command: ["redis-server", "--appendonly", "yes"]
    environment:
      TZ: Asia/Shanghai
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - yudao-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Nacos 注册中心和配置中心 - 简化版本
  nacos:
    image: nacos/nacos-server:v2.3.0
    container_name: yudao-nacos
    restart: unless-stopped
    environment:
      MODE: standalone
      TZ: Asia/Shanghai
    ports:
      - "8848:8848"
      - "9848:9848"
    volumes:
      - nacos_data:/home/<USER>/data
      - nacos_logs:/home/<USER>/logs
    networks:
      - yudao-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  nacos_data:
    driver: local
  nacos_logs:
    driver: local

networks:
  yudao-network:
    driver: bridge
