services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: yudao-postgres-simple
    restart: unless-stopped
    environment:
      POSTGRES_DB: ruoyi-vue-pro
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123456
      TZ: Asia/Shanghai
    ports:
      - "5432:5432"
    volumes:
      - postgres_data_simple:/var/lib/postgresql/data
    networks:
      - yudao-network-simple

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: yudao-redis-simple
    restart: unless-stopped
    command: redis-server --appendonly yes
    environment:
      TZ: Asia/Shanghai
    ports:
      - "6379:6379"
    volumes:
      - redis_data_simple:/data
    networks:
      - yudao-network-simple

volumes:
  postgres_data_simple:
    driver: local
  redis_data_simple:
    driver: local

networks:
  yudao-network-simple:
    driver: bridge
