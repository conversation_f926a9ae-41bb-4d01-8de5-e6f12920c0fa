services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: yudao-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ruoyi-vue-pro
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123456
      TZ: Asia/Shanghai
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/postgresql/ruoyi-vue-pro.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
      - ./sql/postgresql/quartz.sql:/docker-entrypoint-initdb.d/02-quartz.sql:ro
    networks:
      - yudao-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d ruoyi-vue-pro"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: yudao-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    environment:
      TZ: Asia/Shanghai
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - yudao-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  yudao-network:
    driver: bridge
