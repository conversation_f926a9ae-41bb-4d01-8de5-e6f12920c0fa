# 本地开发环境覆盖配置
# 此文件会自动与 docker-compose.yml 合并
version: '3.8'

services:
  # 开发环境下的 PostgreSQL 配置
  postgres:
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: ruoyi-vue-pro
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123456

  # 开发环境下的 Redis 配置
  redis:
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes

  # 开发环境下的 Nacos 配置
  nacos:
    ports:
      - "8848:8848"
      - "9848:9848"
    environment:
      NACOS_AUTH_ENABLE: false  # 开发环境关闭认证

  # 开发环境下的网关服务配置
  gateway-server:
    ports:
      - "48080:48080"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      JAVA_OPTS: -Xms256m -Xmx512m -Djava.security.egd=file:/dev/./urandom

  # 开发环境下的系统服务配置
  system-server:
    ports:
      - "48081:48081"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      JAVA_OPTS: -Xms256m -Xmx512m -Djava.security.egd=file:/dev/./urandom

  # 开发环境下的主服务配置
  yudao-server:
    ports:
      - "9000:9000"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      JAVA_OPTS: -Xms512m -Xmx1024m -Djava.security.egd=file:/dev/./urandom
