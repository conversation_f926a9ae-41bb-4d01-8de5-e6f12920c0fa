# 生产环境 Docker Compose 配置
version: '3.8'

services:
  # PostgreSQL 数据库 - 生产环境配置
  postgres:
    image: postgres:15-alpine
    container_name: yudao-postgres-prod
    restart: always
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
      TZ: Asia/Shanghai
    ports:
      - "127.0.0.1:${POSTGRES_PORT:-5432}:5432"  # 只绑定到本地
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./sql/postgresql/ruoyi-vue-pro.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
      - ./sql/postgresql/quartz.sql:/docker-entrypoint-initdb.d/02-quartz.sql:ro
      - ./backup:/backup  # 备份目录
    networks:
      - yudao-network-prod
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Redis 缓存 - 生产环境配置
  redis:
    image: redis:7-alpine
    container_name: yudao-redis-prod
    restart: always
    command: >
      redis-server 
      --requirepass ${REDIS_PASSWORD}
      --appendonly yes
      --appendfsync everysec
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
    environment:
      TZ: Asia/Shanghai
    ports:
      - "127.0.0.1:${REDIS_PORT:-6379}:6379"  # 只绑定到本地
    volumes:
      - redis_data_prod:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - yudao-network-prod
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Nacos 注册中心和配置中心 - 生产环境配置
  nacos:
    image: nacos/nacos-server:v2.3.0
    container_name: yudao-nacos-prod
    restart: always
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: postgresql
      POSTGRES_SERVICE_HOST: postgres
      POSTGRES_SERVICE_DB_NAME: ${POSTGRES_DB}
      POSTGRES_SERVICE_PORT: 5432
      POSTGRES_SERVICE_USER: ${POSTGRES_USER}
      POSTGRES_SERVICE_PASSWORD: ${POSTGRES_PASSWORD}
      NACOS_AUTH_ENABLE: true
      NACOS_AUTH_TOKEN: ${NACOS_AUTH_TOKEN}
      NACOS_AUTH_IDENTITY_KEY: ${NACOS_AUTH_IDENTITY_KEY}
      NACOS_AUTH_IDENTITY_VALUE: ${NACOS_AUTH_IDENTITY_VALUE}
      NACOS_AUTH_CACHE_ENABLE: true
      JVM_XMS: 512m
      JVM_XMX: 512m
      TZ: Asia/Shanghai
    ports:
      - "127.0.0.1:${NACOS_PORT:-8848}:8848"  # 只绑定到本地
      - "127.0.0.1:${NACOS_GRPC_PORT:-9848}:9848"
    volumes:
      - nacos_data_prod:/home/<USER>/data
      - nacos_logs_prod:/home/<USER>/logs
    networks:
      - yudao-network-prod
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8848/nacos/v1/console/health/readiness"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 120s
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # 网关服务 - 生产环境配置
  gateway-server:
    build:
      context: ./yudao-gateway
      dockerfile: Dockerfile
    image: yudao/gateway-server:latest
    container_name: yudao-gateway-server-prod
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      JAVA_OPTS: >
        ${GATEWAY_JAVA_OPTS}
        -XX:+UseG1GC
        -XX:+PrintGCDetails
        -XX:+PrintGCTimeStamps
        -Xloggc:/home/<USER>/gc.log
        -XX:+HeapDumpOnOutOfMemoryError
        -XX:HeapDumpPath=/home/<USER>/
      TZ: Asia/Shanghai
    ports:
      - "${GATEWAY_PORT:-48080}:48080"
    volumes:
      - gateway_logs_prod:/home/<USER>
      - ./config/gateway:/config:ro
    networks:
      - yudao-network-prod
    depends_on:
      nacos:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:48080/actuator/health"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 180s
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # 系统服务 - 生产环境配置
  system-server:
    build:
      context: ./yudao-module-system/yudao-module-system-server
      dockerfile: Dockerfile
    image: yudao/system-server:latest
    container_name: yudao-system-server-prod
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      JAVA_OPTS: >
        ${SYSTEM_JAVA_OPTS}
        -XX:+UseG1GC
        -XX:+PrintGCDetails
        -XX:+PrintGCTimeStamps
        -Xloggc:/home/<USER>/gc.log
        -XX:+HeapDumpOnOutOfMemoryError
        -XX:HeapDumpPath=/home/<USER>/
      TZ: Asia/Shanghai
    ports:
      - "127.0.0.1:${SYSTEM_PORT:-48081}:48081"  # 只绑定到本地
    volumes:
      - system_logs_prod:/home/<USER>
      - ./config/system:/config:ro
    networks:
      - yudao-network-prod
    depends_on:
      nacos:
        condition: service_healthy
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:48081/actuator/health"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 180s
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # 主服务 - 生产环境配置
  yudao-server:
    build:
      context: ./yudao-server
      dockerfile: Dockerfile
    image: yudao/yudao-server:latest
    container_name: yudao-main-server-prod
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      JAVA_OPTS: >
        ${YUDAO_JAVA_OPTS}
        -XX:+UseG1GC
        -XX:+PrintGCDetails
        -XX:+PrintGCTimeStamps
        -Xloggc:/home/<USER>/gc.log
        -XX:+HeapDumpOnOutOfMemoryError
        -XX:HeapDumpPath=/home/<USER>/
      TZ: Asia/Shanghai
    ports:
      - "127.0.0.1:${YUDAO_PORT:-9000}:9000"  # 只绑定到本地
    volumes:
      - yudao_logs_prod:/home/<USER>
      - ./config/yudao:/config:ro
      - ./uploads:/uploads  # 文件上传目录
    networks:
      - yudao-network-prod
    depends_on:
      nacos:
        condition: service_healthy
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/actuator/health"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 180s
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

volumes:
  postgres_data_prod:
    driver: local
  redis_data_prod:
    driver: local
  nacos_data_prod:
    driver: local
  nacos_logs_prod:
    driver: local
  gateway_logs_prod:
    driver: local
  system_logs_prod:
    driver: local
  yudao_logs_prod:
    driver: local

networks:
  yudao-network-prod:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
