services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: yudao-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-ruoyi-vue-pro}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-123456}
      TZ: Asia/Shanghai
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/postgresql/ruoyi-vue-pro.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
      - ./sql/postgresql/quartz.sql:/docker-entrypoint-initdb.d/02-quartz.sql:ro
    networks:
      - yudao-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-ruoyi-vue-pro}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: yudao-redis
    restart: unless-stopped
    command: ["redis-server", "--appendonly", "yes"]
    environment:
      TZ: Asia/Shanghai
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - yudao-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Nacos 注册中心和配置中心
  nacos:
    image: nacos/nacos-server:v2.3.0
    container_name: yudao-nacos
    restart: unless-stopped
    environment:
      MODE: standalone
      NACOS_AUTH_ENABLE: false
      TZ: Asia/Shanghai
    ports:
      - "${NACOS_PORT:-8848}:8848"
      - "${NACOS_GRPC_PORT:-9848}:9848"
    volumes:
      - nacos_data:/home/<USER>/data
      - nacos_logs:/home/<USER>/logs
    networks:
      - yudao-network
    depends_on: []
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8848/nacos/v1/console/health/readiness"]
      interval: 30s
      timeout: 10s
      retries: 5

  # 网关服务
  gateway-server:
    build:
      context: ./yudao-gateway
      dockerfile: Dockerfile
    container_name: yudao-gateway-server
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: docker
      JAVA_OPTS: ${GATEWAY_JAVA_OPTS:--Xms512m -Xmx1024m}
      TZ: Asia/Shanghai
    ports:
      - "${GATEWAY_PORT:-48080}:48080"
    volumes:
      - gateway_logs:/home/<USER>
    networks:
      - yudao-network
    depends_on:
      nacos:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:48080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # 系统服务
  system-server:
    build:
      context: ./yudao-module-system/yudao-module-system-server
      dockerfile: Dockerfile
    container_name: yudao-system-server
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: docker
      JAVA_OPTS: ${SYSTEM_JAVA_OPTS:--Xms256m -Xmx512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200}
      TZ: Asia/Shanghai
    ports:
      - "${SYSTEM_PORT:-48081}:48081"
    volumes:
      - system_logs:/home/<USER>
    networks:
      - yudao-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:48081/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # 主服务
  yudao-server:
    build:
      context: ./yudao-server
      dockerfile: Dockerfile
    container_name: yudao-main-server
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: docker
      JAVA_OPTS: ${YUDAO_JAVA_OPTS:--Xms1024m -Xmx2048m}
      TZ: Asia/Shanghai
    ports:
      - "${YUDAO_PORT:-9000}:9000"
    volumes:
      - yudao_logs:/home/<USER>
    networks:
      - yudao-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  nacos_data:
    driver: local
  nacos_logs:
    driver: local
  gateway_logs:
    driver: local
  system_logs:
    driver: local
  yudao_logs:
    driver: local

networks:
  yudao-network:
    driver: bridge
