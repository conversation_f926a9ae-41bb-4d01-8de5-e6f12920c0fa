@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 芋道云 Docker 部署脚本 (无代理版本)
:: 作者: Augment Agent

echo.
echo ==================================================
echo        芋道云 Docker 部署脚本 (无代理版本)
echo ==================================================
echo.

:: 临时禁用代理
echo [步骤1] 临时禁用代理设置...
set http_proxy=
set https_proxy=
set HTTP_PROXY=
set HTTPS_PROXY=
set no_proxy=
set NO_PROXY=

echo [信息] 代理已禁用

:: 检查 Docker 是否运行
echo.
echo [步骤2] 检查 Docker 状态...
docker --version >nul 2>&1
if errorlevel 1 (
    echo [错误] Docker 未安装或未启动
    pause
    exit /b 1
)

echo [信息] Docker 检查通过

:: 检查 JAR 文件是否存在
echo.
echo [步骤3] 检查应用 JAR 文件...

if not exist "yudao-gateway\target\yudao-gateway.jar" (
    echo [警告] 未找到 yudao-gateway.jar，请先执行 Maven 构建
    echo [信息] 执行命令: mvn clean package -DskipTests
    pause
    exit /b 1
)

if not exist "yudao-module-system\yudao-module-system-server\target\yudao-module-system-server.jar" (
    echo [警告] 未找到 yudao-module-system-server.jar，请先执行 Maven 构建
    echo [信息] 执行命令: mvn clean package -DskipTests
    pause
    exit /b 1
)

if not exist "yudao-server\target\yudao-server.jar" (
    echo [警告] 未找到 yudao-server.jar，请先执行 Maven 构建
    echo [信息] 执行命令: mvn clean package -DskipTests
    pause
    exit /b 1
)

echo [信息] 所有 JAR 文件检查通过

:: 清理可能的旧容器
echo.
echo [步骤4] 清理旧容器...
docker-compose down >nul 2>&1

:: 拉取镜像
echo.
echo [步骤5] 拉取 Docker 镜像...
docker-compose pull

:: 启动基础服务
echo.
echo [步骤6] 启动基础服务 (PostgreSQL, Redis, Nacos)...
docker-compose up -d postgres redis nacos

echo [信息] 等待基础服务启动...
timeout /t 30 /nobreak >nul

:: 启动应用服务
echo.
echo [步骤7] 启动应用服务...
docker-compose up -d

echo [信息] 等待应用服务启动...
timeout /t 60 /nobreak >nul

:: 显示服务状态
echo.
echo [步骤8] 显示服务状态...
docker-compose ps

:: 显示访问信息
echo.
echo === 芋道云服务访问地址 ===
echo 网关服务:        http://localhost:48080
echo 系统服务:        http://localhost:48081
echo 主服务:          http://localhost:9000
echo Nacos控制台:     http://localhost:8848/nacos
echo PostgreSQL:      localhost:5432
echo Redis:           localhost:6379
echo.
echo === 默认账号信息 ===
echo Nacos:           用户名: nacos, 密码: nacos
echo PostgreSQL:      用户名: postgres, 密码: 123456
echo.
echo [信息] 部署完成！
echo.
pause
