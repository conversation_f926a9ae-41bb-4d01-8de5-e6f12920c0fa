#!/bin/bash

# 芋道云 Docker 部署脚本
# 作者: Augment Agent
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查 Docker 和 Docker Compose 是否安装
check_requirements() {
    print_step "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    print_message "系统要求检查通过"
}

# 构建应用镜像
build_images() {
    print_step "构建应用镜像..."
    
    # 检查是否存在 target 目录和 jar 文件
    if [ ! -f "yudao-gateway/target/yudao-gateway.jar" ]; then
        print_warning "未找到 yudao-gateway.jar，请先执行 Maven 构建"
        print_message "执行命令: mvn clean package -DskipTests"
        exit 1
    fi
    
    if [ ! -f "yudao-module-system/yudao-module-system-server/target/yudao-module-system-server.jar" ]; then
        print_warning "未找到 yudao-module-system-server.jar，请先执行 Maven 构建"
        print_message "执行命令: mvn clean package -DskipTests"
        exit 1
    fi
    
    if [ ! -f "yudao-server/target/yudao-server.jar" ]; then
        print_warning "未找到 yudao-server.jar，请先执行 Maven 构建"
        print_message "执行命令: mvn clean package -DskipTests"
        exit 1
    fi
    
    print_message "所有 JAR 文件检查通过"
}

# 启动基础服务
start_infrastructure() {
    print_step "启动基础服务 (PostgreSQL, Redis, Nacos)..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d postgres redis nacos
    else
        docker compose up -d postgres redis nacos
    fi
    
    print_message "等待基础服务启动..."
    sleep 30
    
    # 检查服务健康状态
    print_step "检查基础服务健康状态..."
    
    # 检查 PostgreSQL
    if command -v docker-compose &> /dev/null; then
        docker-compose exec postgres pg_isready -U postgres -d ruoyi-vue-pro
    else
        docker compose exec postgres pg_isready -U postgres -d ruoyi-vue-pro
    fi
    
    print_message "基础服务启动完成"
}

# 启动应用服务
start_applications() {
    print_step "启动应用服务..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d
    else
        docker compose up -d
    fi
    
    print_message "等待应用服务启动..."
    sleep 60
    
    print_message "应用服务启动完成"
}

# 显示服务状态
show_status() {
    print_step "显示服务状态..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose ps
    else
        docker compose ps
    fi
}

# 显示访问信息
show_access_info() {
    print_step "服务访问信息:"
    echo ""
    echo -e "${GREEN}=== 芋道云服务访问地址 ===${NC}"
    echo -e "${BLUE}网关服务:${NC}        http://localhost:48080"
    echo -e "${BLUE}系统服务:${NC}        http://localhost:48081"
    echo -e "${BLUE}主服务:${NC}          http://localhost:9000"
    echo -e "${BLUE}Nacos控制台:${NC}     http://localhost:8848/nacos"
    echo -e "${BLUE}PostgreSQL:${NC}      localhost:5432"
    echo -e "${BLUE}Redis:${NC}           localhost:6379"
    echo ""
    echo -e "${GREEN}=== 默认账号信息 ===${NC}"
    echo -e "${BLUE}Nacos:${NC}           用户名: nacos, 密码: nacos"
    echo -e "${BLUE}PostgreSQL:${NC}      用户名: postgres, 密码: 123456"
    echo ""
    echo -e "${GREEN}=== 常用命令 ===${NC}"
    echo -e "${BLUE}查看日志:${NC}        docker-compose logs -f [服务名]"
    echo -e "${BLUE}停止服务:${NC}        docker-compose down"
    echo -e "${BLUE}重启服务:${NC}        docker-compose restart [服务名]"
    echo ""
}

# 主函数
main() {
    echo -e "${GREEN}"
    echo "=================================================="
    echo "           芋道云 Docker 部署脚本"
    echo "=================================================="
    echo -e "${NC}"
    
    check_requirements
    build_images
    start_infrastructure
    start_applications
    show_status
    show_access_info
    
    print_message "部署完成！"
}

# 执行主函数
main "$@"
