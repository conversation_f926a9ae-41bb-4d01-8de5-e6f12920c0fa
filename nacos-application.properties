# Nacos ????

# ?????
server.contextPath=/nacos
server.servlet.contextPath=/nacos
server.port=8848

# ??????????????????
# spring.datasource.platform=postgresql
# db.num=1
# db.url.0=*********************************************
# db.user.0=postgres
# db.password.0=123456

# ????
nacos.cmdb.dumpTaskInterval=3600
nacos.cmdb.eventTaskInterval=10
nacos.cmdb.labelTaskInterval=300
nacos.cmdb.loadDataAtStart=false

# ????
nacos.core.auth.enabled=false
nacos.core.auth.system.type=nacos
nacos.core.auth.plugin.nacos.token.secret.key=SecretKey012345678901234567890123456789012345678901234567890123456789

# ????
logging.level.root=INFO
logging.level.com.alibaba.nacos=INFO
