#!/bin/bash

echo "=== 网络诊断脚本 ==="
echo

# 清除代理
echo "1. 清除代理设置..."
unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY no_proxy NO_PROXY
echo "代理已清除"
echo

# 检查基本网络
echo "2. 测试基本网络连接..."
ping -c 3 8.8.8.8
echo

# 检查 DNS
echo "3. 测试 DNS 解析..."
nslookup google.com 8.8.8.8
echo

# 检查 Docker Hub 连接
echo "4. 测试 Docker Hub 连接..."
ping -c 3 registry-1.docker.io
echo

# 检查镜像源连接
echo "5. 测试镜像源连接..."
ping -c 3 docker.mirrors.ustc.edu.cn
echo

# 检查 Docker 状态
echo "6. 检查 Docker 状态..."
docker info | grep -E "(Server Version|Registry Mirrors)"
echo

# 测试拉取小镜像
echo "7. 测试拉取小镜像..."
docker pull hello-world
echo

echo "=== 诊断完成 ==="
