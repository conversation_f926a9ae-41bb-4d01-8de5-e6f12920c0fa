@echo off
chcp 65001 >nul

echo ========================================
echo        芋道云简化启动脚本
echo ========================================
echo.

:: 清除所有代理
set http_proxy=
set https_proxy=
set HTTP_PROXY=
set HTTPS_PROXY=
set no_proxy=
set NO_PROXY=
set all_proxy=
set ALL_PROXY=

echo [信息] 已清除代理设置
echo.

:: 检查 Docker
echo [检查] Docker 状态...
docker --version >nul 2>&1
if errorlevel 1 (
    echo [错误] Docker 未安装或未启动
    pause
    exit /b 1
)

docker info >nul 2>&1
if errorlevel 1 (
    echo [错误] Docker 服务未运行，请启动 Docker Desktop
    pause
    exit /b 1
)

echo [信息] Docker 运行正常
echo.

:: 停止现有服务
echo [操作] 停止现有服务...
docker-compose down >nul 2>&1

:: 启动基础服务
echo [操作] 启动基础服务...
docker-compose up -d postgres redis

echo [信息] 等待基础服务启动...
timeout /t 15 /nobreak >nul

:: 启动 Nacos
echo [操作] 启动 Nacos...
docker-compose up -d nacos

echo [信息] 等待 Nacos 启动...
timeout /t 30 /nobreak >nul

:: 启动应用服务
echo [操作] 启动应用服务...
docker-compose up -d gateway-server system-server yudao-server

echo [信息] 等待应用服务启动...
timeout /t 30 /nobreak >nul

:: 显示状态
echo.
echo [状态] 服务状态:
docker-compose ps

echo.
echo ========================================
echo            启动完成
echo ========================================
echo.
echo 访问地址:
echo 网关服务: http://localhost:48080
echo 系统服务: http://localhost:48081  
echo 主服务:   http://localhost:9000
echo Nacos:    http://localhost:8848/nacos
echo.
pause
