package cn.iocoder.yudao.framework.system.config;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.infra.api.file.dto.FileCreateReqDTO;
import cn.iocoder.yudao.module.infra.api.websocket.WebSocketSenderApi;
import cn.iocoder.yudao.module.infra.api.websocket.dto.WebSocketSendReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 独立模式下的 API 本地实现配置
 * 
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@Slf4j
public class YudaoSystemStandaloneApiConfiguration {

    /**
     * 配置 API 本地实现 - 提供默认配置值
     */
    @Bean
    @ConditionalOnMissingBean
    public ConfigApi configApi() {
        return new ConfigApi() {
            // 默认配置值
            private final Map<String, String> defaultConfigs = new HashMap<String, String>() {{
                put("sys.user.initPassword", "123456");
                put("sys.user.registerEnabled", "false");
            }};

            @Override
            public CommonResult<String> getConfigValueByKey(String key) {
                String value = defaultConfigs.get(key);
                log.debug("[ConfigApi][getConfigValueByKey] 独立模式下获取配置: key={}, value={}", key, value);
                return success(value);
            }
        };
    }

    /**
     * 文件 API 本地实现 - 简单的文件存储
     */
    @Bean
    @ConditionalOnMissingBean
    public FileApi fileApi(@Value("${yudao.file.path:./upload/}") String uploadPath) {
        return new FileApi() {
            @Override
            public CommonResult<String> createFile(FileCreateReqDTO createReqDTO) {
                try {
                    // 创建上传目录
                    Path uploadDir = Paths.get(uploadPath);
                    if (!Files.exists(uploadDir)) {
                        Files.createDirectories(uploadDir);
                    }

                    // 生成文件名
                    String fileName = createReqDTO.getName();
                    if (fileName == null || fileName.isEmpty()) {
                        fileName = UUID.randomUUID().toString();
                    }

                    // 添加目录前缀
                    String directory = createReqDTO.getDirectory();
                    if (directory != null && !directory.isEmpty()) {
                        Path dirPath = uploadDir.resolve(directory);
                        if (!Files.exists(dirPath)) {
                            Files.createDirectories(dirPath);
                        }
                        fileName = directory + "/" + fileName;
                    }

                    // 写入文件
                    Path filePath = uploadDir.resolve(fileName);
                    Files.write(filePath, createReqDTO.getContent());

                    String relativePath = "/" + fileName;
                    log.info("[FileApi][createFile] 独立模式下创建文件成功: {}", relativePath);
                    return success(relativePath);
                } catch (IOException e) {
                    log.error("[FileApi][createFile] 创建文件失败", e);
                    return CommonResult.error(500, "文件创建失败: " + e.getMessage());
                }
            }
        };
    }

    /**
     * WebSocket 发送器 API 本地实现
     * 在独立模式下，简单记录日志，不实际发送
     */
    @Bean
    @ConditionalOnMissingBean
    public WebSocketSenderApi webSocketSenderApi() {
        return new WebSocketSenderApi() {
            @Override
            public CommonResult<Boolean> send(WebSocketSendReqDTO sendReqDTO) {
                log.info("[WebSocketSenderApi][send] 独立模式下模拟发送 WebSocket 消息: {}", sendReqDTO);
                return success(true);
            }

            @Override
            public void sendObject(Integer userType, Long userId, String messageType, Object messageContent) {
                log.info("[WebSocketSenderApi][sendObject] 独立模式下模拟发送 WebSocket 消息: userType={}, userId={}, messageType={}, content={}", 
                    userType, userId, messageType, messageContent);
            }

            @Override
            public void sendObject(Integer userType, String messageType, Object messageContent) {
                log.info("[WebSocketSenderApi][sendObject] 独立模式下模拟发送 WebSocket 广播消息: userType={}, messageType={}, content={}", 
                    userType, messageType, messageContent);
            }

            @Override
            public void sendObject(String sessionId, String messageType, Object messageContent) {
                log.info("[WebSocketSenderApi][sendObject] 独立模式下模拟发送 WebSocket 会话消息: sessionId={}, messageType={}, content={}", 
                    sessionId, messageType, messageContent);
            }
        };
    }

} 