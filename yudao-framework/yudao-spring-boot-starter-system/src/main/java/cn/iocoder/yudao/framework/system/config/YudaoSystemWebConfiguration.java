package cn.iocoder.yudao.framework.system.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 系统模块 Web 配置类
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnWebApplication
@ConditionalOnProperty(prefix = "yudao.system", name = "enabled", havingValue = "true", matchIfMissing = true)
@ComponentScan(basePackages = {
    "cn.iocoder.yudao.module.system.controller",
    "cn.iocoder.yudao.module.system.api"
})
@Slf4j
public class YudaoSystemWebConfiguration {

    public YudaoSystemWebConfiguration() {
        log.info("[YudaoSystemWebConfiguration] 系统模块 Web 配置加载完成");
    }

} 