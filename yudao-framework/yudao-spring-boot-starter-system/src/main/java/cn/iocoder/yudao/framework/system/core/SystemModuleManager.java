package cn.iocoder.yudao.framework.system.core;

import cn.iocoder.yudao.framework.system.config.YudaoSystemProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;

/**
 * 系统模块管理器
 * 负责管理系统模块的生命周期和配置
 *
 * <AUTHOR>
 */
@Slf4j
public class SystemModuleManager implements InitializingBean {

    private final YudaoSystemProperties properties;

    public SystemModuleManager(YudaoSystemProperties properties) {
        this.properties = properties;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("========================================");
        log.info("芋道系统模块启动完成！");
        log.info("运行模式: {}", getRunningMode());
        log.info("启用功能: {}", getEnabledFeatures());
        log.info("========================================");
    }

    /**
     * 获取运行模式
     */
    private String getRunningMode() {
        if (properties.getMode().getStandalone()) {
            return "独立运行模式";
        } else if (properties.getMode().getMicroservice()) {
            return "微服务模式";
        } else {
            return "集成模式";
        }
    }

    /**
     * 获取启用的功能
     */
    private String getEnabledFeatures() {
        StringBuilder features = new StringBuilder();
        
        if (properties.getUser().getEnabled()) {
            features.append("用户管理 ");
        }
        if (properties.getPermission().getEnabled()) {
            features.append("权限管理 ");
        }
        if (properties.getTenant().getEnabled()) {
            features.append("多租户 ");
        }
        if (properties.getSocial().getEnabled()) {
            features.append("社交登录 ");
        }
        if (properties.getSms().getEnabled()) {
            features.append("短信服务 ");
        }
        if (properties.getMail().getEnabled()) {
            features.append("邮件服务 ");
        }
        
        return features.toString().trim();
    }

    /**
     * 获取系统模块配置
     */
    public YudaoSystemProperties getProperties() {
        return properties;
    }

} 