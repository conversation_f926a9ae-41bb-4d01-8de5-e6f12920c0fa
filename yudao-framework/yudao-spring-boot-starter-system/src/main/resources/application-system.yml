yudao:
  system:
    # 是否启用系统模块
    enabled: true
    
    # 运行模式配置
    mode:
      # 独立运行模式（默认关闭）
      standalone: false
      # 微服务模式（默认关闭）
      microservice: false
    
    # 用户管理配置
    user:
      enabled: true
      default-password: "123456"
      password-encoder: "BCrypt"
    
    # 权限管理配置
    permission:
      enabled: true
      data-permission: true
    
    # 租户管理配置
    tenant:
      enabled: true
    
    # 社交登录配置（默认关闭）
    social:
      enabled: false
    
    # 短信配置（默认关闭）
    sms:
      enabled: false
    
    # 邮件配置（默认关闭）
    mail:
      enabled: false

# 当启用独立运行模式时的配置
---
spring:
  config:
    activate:
      on-profile: system-standalone

yudao:
  system:
    mode:
      standalone: true
    social:
      enabled: true
    sms:
      enabled: true
    mail:
      enabled: true

server:
  port: 48080
  servlet:
    context-path: /admin-api

# 当启用微服务模式时的配置
---
spring:
  config:
    activate:
      on-profile: system-microservice

yudao:
  system:
    mode:
      microservice: true

spring:
  application:
    name: yudao-system-service
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
      config:
        server-addr: 127.0.0.1:8848
        file-extension: yaml 