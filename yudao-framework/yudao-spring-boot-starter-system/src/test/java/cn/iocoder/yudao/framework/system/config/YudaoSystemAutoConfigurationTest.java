package cn.iocoder.yudao.framework.system.config;

import org.junit.jupiter.api.Test;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.context.annotation.Configuration;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * {@link YudaoSystemProperties} 的单元测试
 *
 * <AUTHOR>
 */
class YudaoSystemAutoConfigurationTest {

    @Configuration
    @EnableConfigurationProperties(YudaoSystemProperties.class)
    static class TestConfiguration {
    }

    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
            .withUserConfiguration(TestConfiguration.class);

    @Test
    void testPropertiesEnabled() {
        this.contextRunner
                .withPropertyValues("yudao.system.enabled=true")
                .run(context -> {
                    assertThat(context).hasSingleBean(YudaoSystemProperties.class);
                    YudaoSystemProperties properties = context.getBean(YudaoSystemProperties.class);
                    assertThat(properties.getEnabled()).isTrue();
                });
    }

    @Test
    void testPropertiesDisabled() {
        this.contextRunner
                .withPropertyValues("yudao.system.enabled=false")
                .run(context -> {
                    assertThat(context).hasSingleBean(YudaoSystemProperties.class);
                    YudaoSystemProperties properties = context.getBean(YudaoSystemProperties.class);
                    assertThat(properties.getEnabled()).isFalse();
                });
    }

    @Test
    void testStandaloneModeProperties() {
        this.contextRunner
                .withPropertyValues(
                        "yudao.system.enabled=true",
                        "yudao.system.mode.standalone=true"
                )
                .run(context -> {
                    assertThat(context).hasSingleBean(YudaoSystemProperties.class);
                    YudaoSystemProperties properties = context.getBean(YudaoSystemProperties.class);
                    assertThat(properties.getEnabled()).isTrue();
                    assertThat(properties.getMode().getStandalone()).isTrue();
                });
    }

    @Test
    void testMicroserviceModeProperties() {
        this.contextRunner
                .withPropertyValues(
                        "yudao.system.enabled=true",
                        "yudao.system.mode.microservice=true"
                )
                .run(context -> {
                    assertThat(context).hasSingleBean(YudaoSystemProperties.class);
                    YudaoSystemProperties properties = context.getBean(YudaoSystemProperties.class);
                    assertThat(properties.getEnabled()).isTrue();
                    assertThat(properties.getMode().getMicroservice()).isTrue();
                });
    }

    @Test
    void testDefaultProperties() {
        this.contextRunner
                .run(context -> {
                    assertThat(context).hasSingleBean(YudaoSystemProperties.class);
                    YudaoSystemProperties properties = context.getBean(YudaoSystemProperties.class);
                    assertThat(properties.getEnabled()).isTrue();
                    assertThat(properties.getUser().getEnabled()).isTrue();
                    assertThat(properties.getPermission().getEnabled()).isTrue();
                    assertThat(properties.getTenant().getEnabled()).isTrue();
                    assertThat(properties.getSocial().getEnabled()).isFalse();
                    assertThat(properties.getSms().getEnabled()).isFalse();
                    assertThat(properties.getMail().getEnabled()).isFalse();
                });
    }
} 