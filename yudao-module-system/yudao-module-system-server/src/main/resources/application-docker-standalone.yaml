# Docker 独立运行配置（不依赖 Nacos）

server:
  port: 48081

spring:
  application:
    name: system-server
  
  # 完全禁用 Nacos
  cloud:
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false
    discovery:
      enabled: false
    config:
      enabled: false
  
  # 数据源配置项
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
        login-username: # 控制台管理用户名和密码
        login-password:
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 慢 SQL 记录
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 1 # 初始连接数
        min-idle: 1 # 最小连接池数量
        max-active: 20 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
      datasource:
        master:
          url: ********************************************* # PostgreSQL 连接
          username: postgres
          password: 123456
          driver-class-name: org.postgresql.Driver

  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  data:
    redis:
      host: redis # Redis 地址
      port: 6379 # 端口
      database: 0 # 数据库索引
      # password: # 密码，建议生产环境开启

# 监控端点
management:
  endpoints:
    web:
      base-path: /actuator # Actuator 提供的 API 接口的根目录。默认为 /actuator
      exposure:
        include: 'health,info' # 只开放必要的端点
  endpoint:
    health:
      show-details: always

# 日志配置
logging:
  file:
    name: /home/<USER>/system-server.log # 日志文件名，全路径
  level:
    root: INFO
    cn.iocoder.yudao.module.system: DEBUG
    org.springframework.context.support.PostProcessorRegistrationDelegate: ERROR # TODO 芋艿：先禁用，Spring Boot 3.X 存在部分错误的 WARN 提示

debug: false

# 芋道配置项，设置当前项目所有自定义的配置
yudao:
  env: # 多环境的配置项
    tag: ${HOSTNAME}
  captcha:
    enable: false # Docker环境，暂时关闭图片验证码，方便登录等接口的测试
  security:
    mock-enable: false # Docker环境关闭mock
  access-log: # 访问日志的配置项
    enable: true
  tenant: # 多租户相关配置项
    enable: true
    ignore-urls:
    ignore-visit-urls:
      - /admin-api/system/user/profile/**
      - /admin-api/system/auth/**
    ignore-tables:
    ignore-caches:
      - user_role_ids
      - permission_menu_ids
      - oauth_client
      - notify_template
      - mail_account
      - mail_template
      - sms_template

# 定时任务相关配置
xxl:
  job:
    enabled: false # 是否开启调度中心，默认为 true 开启

# Lock4j 配置项
lock4j:
  acquire-timeout: 3000 # 获取分布式锁超时时间，默认为 3000 毫秒
  expire: 30000 # 分布式锁的超时时间，默认为 30 毫秒
