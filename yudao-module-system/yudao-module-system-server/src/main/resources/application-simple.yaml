# 极简化的 Docker 配置，完全禁用复杂功能

server:
  port: 48081

spring:
  application:
    name: system-server

  # 完全禁用 Nacos
  cloud:
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false

  # 数据源配置项
  datasource:
    dynamic: # 多数据源配置
      primary: master
      datasource:
        master:
          url: *********************************************
          username: postgres
          password: 123456
          driver-class-name: org.postgresql.Driver

  # Redis 配置
  data:
    redis:
      host: redis
      port: 6379
      database: 0

# 监控端点
management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: 'health,info'
  endpoint:
    health:
      show-details: always

# 极简日志配置
logging:
  level:
    root: WARN
    cn.iocoder.yudao: INFO
    com.alibaba.nacos: ERROR
    org.springframework: WARN

# 芋道配置项 - 完全禁用复杂功能
yudao:
  captcha:
    enable: false
  security:
    mock-enable: false
  tenant:
    enable: false
  xss:
    enable: false
  demo:
    enable: false
