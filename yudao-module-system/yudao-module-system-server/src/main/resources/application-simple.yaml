# 最简化的 Docker 配置，用于调试启动问题

server:
  port: 48081

spring:
  application:
    name: system-server
  
  # 数据源配置项
  datasource:
    dynamic: # 多数据源配置
      primary: master
      datasource:
        master:
          url: *********************************************
          username: postgres
          password: 123456
          driver-class-name: org.postgresql.Driver

  # Redis 配置
  data:
    redis:
      host: redis
      port: 6379
      database: 0

# 监控端点
management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: 'health,info'
  endpoint:
    health:
      show-details: always

# 日志配置 - 简化版本
logging:
  level:
    root: INFO
    cn.iocoder.yudao: DEBUG

# 芋道配置项 - 最简化
yudao:
  captcha:
    enable: false
  security:
    mock-enable: false
  tenant:
    enable: false
