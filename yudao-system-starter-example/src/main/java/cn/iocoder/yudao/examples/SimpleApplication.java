package cn.iocoder.yudao.examples;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 简化的芋道系统模块示例应用
 * 
 * 只包含最基础的功能，不包含社交登录等复杂功能
 * 
 * <AUTHOR>
 */
@SpringBootApplication(
    exclude = {
        // 排除所有可能有问题的自动配置
        cn.iocoder.yudao.framework.apilog.config.YudaoApiLogAutoConfiguration.class,
        cn.iocoder.yudao.framework.security.config.YudaoSecurityAutoConfiguration.class,
        cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration.class,
        cn.iocoder.yudao.framework.web.config.YudaoWebAutoConfiguration.class,
        // 排除 Feign 相关配置
        org.springframework.cloud.openfeign.FeignAutoConfiguration.class,
        org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration.class
    },
    scanBasePackages = {
        // 只扫描我们自己的包
        "cn.iocoder.yudao.examples"
    }
)
@MapperScan(basePackages = {
    "cn.iocoder.yudao.examples"
})
public class SimpleApplication {

    public static void main(String[] args) {
        SpringApplication.run(SimpleApplication.class, args);
    }
} 