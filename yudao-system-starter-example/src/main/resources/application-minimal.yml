# 真正最小化配置 - 彻底禁用所有复杂功能
server:
  port: 18080

spring:
  application:
    name: yudao-system-minimal
  
  # 关键配置：允许覆盖和循环引用
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
    lazy-initialization: false
  
  # 彻底禁用 Spring Cloud
  cloud:
    enabled: false
    discovery:
      enabled: false
    config:
      enabled: false
    loadbalancer:
      enabled: false
    openfeign:
      enabled: false
    bootstrap:
      enabled: false
    nacos:
      enabled: false
  
  # 禁用 Spring Security
  security:
    enabled: false
  
  # 排除所有可能有问题的自动配置
  autoconfigure:
    exclude:
      # 数据库相关
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
      # Spring Cloud 相关
      - org.springframework.cloud.openfeign.FeignAutoConfiguration
      - org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration
      - org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration
      - org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration
      # Redis 相关
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.redisson.spring.starter.RedissonAutoConfigurationV2
      # 芋道框架相关
      - cn.iocoder.yudao.framework.apilog.config.YudaoApiLogAutoConfiguration
      - cn.iocoder.yudao.framework.security.config.YudaoSecurityAutoConfiguration
      - cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration
      - cn.iocoder.yudao.framework.web.config.YudaoWebAutoConfiguration
      - cn.iocoder.yudao.framework.datapermission.config.YudaoDataPermissionAutoConfiguration
      # Spring Security 相关
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      - org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration
      - org.springframework.boot.autoconfigure.security.servlet.SecurityFilterAutoConfiguration
      # 其他可能有问题的配置
      - com.mzt.logapi.starter.configuration.LogRecordProxyAutoConfiguration

# 芋道框架基础配置
yudao:
  info:
    version: 1.0.0
    base-package: cn.iocoder.yudao.examples
  web:
    admin-ui:
      url: http://localhost:18080
  captcha:
    enable: false

# 日志配置
logging:
  level:
    root: INFO
    cn.iocoder.yudao: DEBUG
    org.springframework.cloud: WARN
    org.springframework.security: WARN 