# 能够成功启动的配置
server:
  port: 18080

spring:
  application:
    name: yudao-system-working
  
  # 关键配置
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
    lazy-initialization: false
  
  # 禁用数据源和其他可能有问题的自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
      - org.springframework.cloud.openfeign.FeignAutoConfiguration
      - org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.redisson.spring.starter.RedissonAutoConfigurationV2

# 芋道框架配置
yudao:
  info:
    version: 1.0.0
    base-package: cn.iocoder.yudao.examples
  web:
    admin-ui:
      url: http://localhost:18080  # 必须配置，不能为空
    # 添加 Web 相关配置
    api-prefix: /admin-api
    controller-package: cn.iocoder.yudao.examples.controller
  # 禁用验证码
  captcha:
    enable: false

# 日志配置
logging:
  level:
    cn.iocoder.yudao: INFO
    org.springframework.cloud: WARN
    root: INFO 